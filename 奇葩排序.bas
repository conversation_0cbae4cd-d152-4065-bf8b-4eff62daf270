' 奇葩排序 - CorelDRAW VBA 宏
' 功能：按宽度、高度、面积、图层排序并排列对象

' 文本框输入验证函数
Private Function ValidateNumericInput(inputText As String) As Boolean
    Dim i As Integer
    Dim dotCount As Integer
    dotCount = 0

    If Len(inputText) = 0 Then
        ValidateNumericInput = False
        Exit Function
    End If

    For i = 1 To Len(inputText)
        Dim char As String
        char = Mid(inputText, i, 1)
        If char = "." Then
            dotCount = dotCount + 1
            If dotCount > 1 Then
                ValidateNumericInput = False
                Exit Function
            End If
        ElseIf Not (char >= "0" And char <= "9") Then
            ValidateNumericInput = False
            Exit Function
        End If
    Next i

    ValidateNumericInput = True
End Function

' 按宽度排序按钮事件
Private Sub KDPX_Click()
    SortAndArrangeShapes "width"
End Sub

' 按高度排序按钮事件
Private Sub GDPX_Click()
    SortAndArrangeShapes "height"
End Sub

' 按面积排序按钮事件
Private Sub MJPX_Click()
    SortAndArrangeShapes "area"
End Sub

' 按图层排序按钮事件
Private Sub TCPX_Click()
    SortAndArrangeShapes "layer"
End Sub

' 排列文本框输入验证
Private Sub PL_Change()
    If Not ValidateNumericInput(PL.Text) Then
        MsgBox "排列数量只能输入数字和一个小数点！", vbExclamation
        PL.Text = "10"
    End If
End Sub

' 横距文本框输入验证
Private Sub HJ_Change()
    If Not ValidateNumericInput(HJ.Text) Then
        MsgBox "横距只能输入数字和一个小数点！", vbExclamation
        HJ.Text = "30"
    End If
End Sub

' 纵距文本框输入验证
Private Sub ZJ_Change()
    If Not ValidateNumericInput(ZJ.Text) Then
        MsgBox "纵距只能输入数字和一个小数点！", vbExclamation
        ZJ.Text = "30"
    End If
End Sub

' 主要排序和排列函数
Private Sub SortAndArrangeShapes(sortType As String)
    On Error GoTo ErrorHandler

    ' 检查是否有选择的对象
    If ActiveSelection.Shapes.Count = 0 Then
        MsgBox "请先选择要排序的对象！", vbExclamation
        Exit Sub
    End If

    ' 设置文档单位为毫米
    ActiveDocument.Unit = cdrMillimeter

    ' 开始命令组，方便撤销
    ActiveDocument.BeginCommandGroup "奇葩排序"

    ' 获取排序参数
    Dim isAscending As Boolean
    isAscending = ZX.Value ' True为正序，False为逆序

    ' 获取排列参数
    Dim arrangeCount As Integer
    Dim horizontalSpacing As Double
    Dim verticalSpacing As Double
    Dim isHorizontal As Boolean

    arrangeCount = CInt(Val(PL.Text))
    horizontalSpacing = CDbl(Val(HJ.Text))
    verticalSpacing = CDbl(Val(ZJ.Text))
    isHorizontal = H.Value ' True为横向，False为纵向

    ' 创建形状数组用于排序
    Dim shapeArray() As Shape
    Dim sortValues() As Double
    Dim i As Integer, j As Integer
    Dim shapeCount As Integer

    shapeCount = ActiveSelection.Shapes.Count
    ReDim shapeArray(1 To shapeCount)
    ReDim sortValues(1 To shapeCount)

    ' 填充数组并计算排序值
    For i = 1 To shapeCount
        Set shapeArray(i) = ActiveSelection.Shapes(i)

        Select Case sortType
            Case "width"
                sortValues(i) = shapeArray(i).SizeWidth
            Case "height"
                sortValues(i) = shapeArray(i).SizeHeight
            Case "area"
                sortValues(i) = shapeArray(i).SizeWidth * shapeArray(i).SizeHeight
            Case "layer"
                sortValues(i) = shapeArray(i).Layer.Index
        End Select
    Next i

    ' 冒泡排序
    For i = 1 To shapeCount - 1
        For j = i + 1 To shapeCount
            Dim shouldSwap As Boolean
            If isAscending Then
                shouldSwap = sortValues(i) > sortValues(j)
            Else
                shouldSwap = sortValues(i) < sortValues(j)
            End If

            If shouldSwap Then
                ' 交换排序值
                Dim tempValue As Double
                tempValue = sortValues(i)
                sortValues(i) = sortValues(j)
                sortValues(j) = tempValue

                ' 交换形状
                Dim tempShape As Shape
                Set tempShape = shapeArray(i)
                Set shapeArray(i) = shapeArray(j)
                Set shapeArray(j) = tempShape
            End If
        Next j
    Next i

    ' 排列形状
    ArrangeShapes shapeArray, arrangeCount, horizontalSpacing, verticalSpacing, isHorizontal

    ' 结束命令组
    ActiveDocument.EndCommandGroup

    MsgBox "排序和排列完成！", vbInformation
    Exit Sub

ErrorHandler:
    ActiveDocument.EndCommandGroup
    MsgBox "操作过程中发生错误：" & Err.Description, vbCritical
End Sub

' 排列形状函数
Private Sub ArrangeShapes(shapeArray() As Shape, arrangeCount As Integer, hSpacing As Double, vSpacing As Double, isHorizontal As Boolean)
    Dim i As Integer
    Dim currentRow As Integer
    Dim currentCol As Integer
    Dim startX As Double, startY As Double
    Dim currentX As Double, currentY As Double

    ' 获取第一个形状的位置作为起始位置
    shapeArray(1).GetPosition startX, startY
    currentX = startX
    currentY = startY

    ' 设置第一个形状的位置
    shapeArray(1).SetPosition currentX, currentY

    currentRow = 1
    currentCol = 1

    ' 排列其余形状
    For i = 2 To UBound(shapeArray)
        If isHorizontal Then
            ' 横向排列
            If currentCol >= arrangeCount Then
                ' 换行
                currentRow = currentRow + 1
                currentCol = 1
                currentX = startX
                currentY = startY - (currentRow - 1) * vSpacing
            Else
                ' 同行下一个位置
                currentCol = currentCol + 1
                currentX = currentX + hSpacing
            End If
        Else
            ' 纵向排列
            If currentRow >= arrangeCount Then
                ' 换列
                currentCol = currentCol + 1
                currentRow = 1
                currentY = startY
                currentX = startX + (currentCol - 1) * hSpacing
            Else
                ' 同列下一个位置
                currentRow = currentRow + 1
                currentY = currentY - vSpacing
            End If
        End If

        ' 设置形状位置
        shapeArray(i).SetPosition currentX, currentY
    Next i
End Sub