Private Function ValidateNumericInput(inputText As String) As Boolean
    Dim i As Integer
    Dim dotCount As Integer
    dotCount = 0
    If Len(inputText) = 0 Then
        ValidateNumericInput = False
        Exit Function
    End If
    For i = 1 To Len(inputText)
        Dim char As String
        char = Mid(inputText, i, 1)
        If char = "." Then
            dotCount = dotCount + 1
            If dotCount > 1 Then
                ValidateNumericInput = False
                Exit Function
            End If
        ElseIf Not (char >= "0" And char <= "9") Then
            ValidateNumericInput = False
            Exit Function
        End If
    Next i
    ValidateNumericInput = True
End Function

Private Sub KDPX_Click()
    SortAndArrangeShapes "width"
End Sub

Private Sub GDPX_Click()
    SortAndArrangeShapes "height"
End Sub

Private Sub MJPX_Click()
    SortAndArrangeShapes "area"
End Sub

Private Sub TCPX_Click()
    SortAndArrangeShapes "layer"
End Sub

Private Sub PL_Change()
    If Not ValidateNumericInput(PL.Text) Then
        PL.Text = "10"
    End If
End Sub

Private Sub HJ_Change()
    If Not ValidateNumericInput(HJ.Text) Then
        HJ.Text = "30"
    End If
End Sub

Private Sub ZJ_Change()
    If Not ValidateNumericInput(ZJ.Text) Then
        ZJ.Text = "30"
    End If
End Sub

Private Sub SortAndArrangeShapes(sortType As String)
    On Error GoTo ErrorHandler
    If ActiveSelection.Shapes.Count = 0 Then Exit Sub
    ActiveDocument.Unit = cdrMillimeter
    ActiveDocument.BeginCommandGroup "奇葩排序"

    Dim isAscending As Boolean
    isAscending = ZX.Value
    Dim arrangeCount As Integer
    Dim horizontalSpacing As Double
    Dim verticalSpacing As Double
    Dim isHorizontal As Boolean
    arrangeCount = CInt(Val(PL.Text))
    horizontalSpacing = CDbl(Val(HJ.Text))
    verticalSpacing = CDbl(Val(ZJ.Text))
    isHorizontal = H.Value

    Dim shapeArray() As Shape
    Dim sortValues() As Double
    Dim i As Integer, j As Integer
    Dim shapeCount As Integer
    shapeCount = ActiveSelection.Shapes.Count
    ReDim shapeArray(1 To shapeCount)
    ReDim sortValues(1 To shapeCount)

    For i = 1 To shapeCount
        Set shapeArray(i) = ActiveSelection.Shapes(i)
        Select Case sortType
            Case "width"
                sortValues(i) = shapeArray(i).SizeWidth
            Case "height"
                sortValues(i) = shapeArray(i).SizeHeight
            Case "area"
                sortValues(i) = shapeArray(i).SizeWidth * shapeArray(i).SizeHeight
            Case "layer"
                sortValues(i) = shapeArray(i).Layer.Index
        End Select
    Next i

    For i = 1 To shapeCount - 1
        For j = i + 1 To shapeCount
            Dim shouldSwap As Boolean
            If isAscending Then
                shouldSwap = sortValues(i) > sortValues(j)
            Else
                shouldSwap = sortValues(i) < sortValues(j)
            End If
            If shouldSwap Then
                Dim tempValue As Double
                tempValue = sortValues(i)
                sortValues(i) = sortValues(j)
                sortValues(j) = tempValue
                Dim tempShape As Shape
                Set tempShape = shapeArray(i)
                Set shapeArray(i) = shapeArray(j)
                Set shapeArray(j) = tempShape
            End If
        Next j
    Next i

    ArrangeShapes shapeArray, arrangeCount, horizontalSpacing, verticalSpacing, isHorizontal
    ActiveDocument.EndCommandGroup
    Exit Sub
ErrorHandler:
    ActiveDocument.EndCommandGroup
End Sub

Private Sub ArrangeShapes(shapeArray() As Shape, arrangeCount As Integer, hSpacing As Double, vSpacing As Double, isHorizontal As Boolean)
    Dim i As Integer
    Dim currentRow As Integer
    Dim currentCol As Integer
    Dim startX As Double, startY As Double
    Dim currentX As Double, currentY As Double
    shapeArray(1).GetPosition startX, startY
    currentRow = 0
    currentCol = 0

    For i = 1 To UBound(shapeArray)
        If isHorizontal Then
            If i Mod arrangeCount = 1 Then
                currentRow = currentRow + 1
                currentCol = 1
                currentX = startX
                If currentRow = 1 Then
                    currentY = startY
                Else
                    currentY = currentY - GetMaxHeightInPrevRow(shapeArray, i - arrangeCount, arrangeCount) - vSpacing
                End If
            Else
                currentCol = currentCol + 1
                currentX = currentX + shapeArray(i - 1).SizeWidth + hSpacing
            End If
        Else
            If i Mod arrangeCount = 1 Then
                currentCol = currentCol + 1
                currentRow = 1
                currentY = startY
                If currentCol = 1 Then
                    currentX = startX
                Else
                    currentX = currentX + GetMaxWidthInPrevCol(shapeArray, i - arrangeCount, arrangeCount) + hSpacing
                End If
            Else
                currentRow = currentRow + 1
                currentY = currentY - shapeArray(i - 1).SizeHeight - vSpacing
            End If
        End If
        shapeArray(i).SetPosition currentX, currentY
    Next i
End Sub

Private Function GetMaxHeightInPrevRow(shapeArray() As Shape, startIndex As Integer, arrangeCount As Integer) As Double
    Dim maxHeight As Double
    Dim i As Integer
    Dim endIndex As Integer
    maxHeight = 0
    endIndex = startIndex + arrangeCount - 1
    If endIndex > UBound(shapeArray) Then endIndex = UBound(shapeArray)
    For i = startIndex To endIndex
        If shapeArray(i).SizeHeight > maxHeight Then
            maxHeight = shapeArray(i).SizeHeight
        End If
    Next i
    GetMaxHeightInPrevRow = maxHeight
End Function

Private Function GetMaxWidthInPrevCol(shapeArray() As Shape, startIndex As Integer, arrangeCount As Integer) As Double
    Dim maxWidth As Double
    Dim i As Integer
    Dim endIndex As Integer
    maxWidth = 0
    endIndex = startIndex + arrangeCount - 1
    If endIndex > UBound(shapeArray) Then endIndex = UBound(shapeArray)
    For i = startIndex To endIndex
        If shapeArray(i).SizeWidth > maxWidth Then
            maxWidth = shapeArray(i).SizeWidth
        End If
    Next i
    GetMaxWidthInPrevCol = maxWidth
End Function